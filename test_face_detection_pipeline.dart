import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/services/face_detection_service.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/camera_preview_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// Test script to verify face detection pipeline fixes
void main() {
  group('Face Detection Pipeline Tests', () {
    test('FaceDetectionService initializes with optimized options', () async {
      final service = FaceDetectionService();

      // Test initialization
      await service.initialize();
      expect(service.isInitialized, isTrue);

      // Test configuration
      final config = service.getConfiguration();
      expect(config['isInitialized'], isTrue);
      expect(config['minimumCoverageThreshold'], equals(80));

      // Cleanup
      await service.dispose();
    });

    test('FaceDetectionResult model works correctly', () {
      final result = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 85,
        timestamp: DateTime.now(),
      );

      expect(result.faceDetected, isTrue);
      expect(result.faceCount, equals(1));
      expect(result.coveragePercentage, equals(85.0));
      expect(result.meetsThreshold, isTrue);
      expect(result.isValidDetection, isTrue);
    });

    test('FaceDetectionResult threshold validation', () {
      // Test below threshold
      final belowThreshold = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 75,
        timestamp: DateTime.now(),
      );
      expect(belowThreshold.meetsThreshold, isFalse);
      expect(belowThreshold.isValidDetection, isFalse);

      // Test above threshold
      final aboveThreshold = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 85,
        timestamp: DateTime.now(),
      );
      expect(aboveThreshold.meetsThreshold, isTrue);
      expect(aboveThreshold.isValidDetection, isTrue);

      // Test multiple faces
      final multipleFaces = FaceDetectionResult(
        faceDetected: true,
        faceCount: 2,
        coveragePercentage: 85,
        timestamp: DateTime.now(),
      );
      expect(multipleFaces.isValidDetection, isFalse);
    });

    testWidgets('CameraPreviewWidget builds without errors', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CameraPreviewWidget(),
          ),
        ),
      );

      // Verify widget builds
      expect(find.byType(CameraPreviewWidget), findsOneWidget);
    });

    group('ProcessFrame Event Tests', () {
      test('ProcessFrame event contains detection result', () {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        );

        final event = ProcessFrame(detectionResult);
        expect(event.detectionResult, equals(detectionResult));
        expect(event.detectionResult.isValidDetection, isTrue);
      });
    });
  });

  group('Face Detection Pipeline Integration', () {
    test('Detection results flow correctly', () {
      final results = <FaceDetectionResult>[];

      // Simulate frame processing
      for (var i = 0; i < 10; i++) {
        final result = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 80.0 + (i * 2), // Increasing coverage
          timestamp: DateTime.now(),
        );
        results.add(result);
      }

      expect(results.length, equals(10));
      expect(results.first.coveragePercentage, equals(80.0));
      expect(results.last.coveragePercentage, equals(98.0));

      // All should meet threshold
      final validResults = results.where((r) => r.isValidDetection).length;
      expect(validResults, equals(10));
    });

    test('Coverage statistics calculation', () {
      final results = [
        FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        ),
        FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 90,
          timestamp: DateTime.now(),
        ),
        FaceDetectionResult(
          faceDetected: false,
          faceCount: 0,
          coveragePercentage: 0,
          timestamp: DateTime.now(),
        ),
      ];

      final framesWithFace = results.where((r) => r.faceDetected).length;
      final framesWithValidCoverage =
          results.where((r) => r.meetsThreshold).length;

      expect(framesWithFace, equals(2));
      expect(framesWithValidCoverage, equals(2));

      final coverageValues = results.map((r) => r.coveragePercentage).toList();
      final averageCoverage =
          coverageValues.reduce((a, b) => a + b) / coverageValues.length;
      expect(averageCoverage, closeTo(58.33, 0.1));
    });
  });

  group('Performance Tests', () {
    test('Face detection result creation is fast', () {
      final stopwatch = Stopwatch()..start();

      for (var i = 0; i < 1000; i++) {
        FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        );
      }

      stopwatch.stop();
      expect(stopwatch.elapsedMilliseconds, lessThan(100));
    });
  });
}
