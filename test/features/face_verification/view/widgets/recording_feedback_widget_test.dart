import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/recording_feedback_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('RecordingFeedbackWidget', () {
    testWidgets('displays recording time correctly',
        (WidgetTester tester) async {
      // Arrange
      const elapsedTime = Duration(seconds: 4);
      const remainingTime = Duration(seconds: 5);
      const progress = 0.44; // 4/9 seconds elapsed

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: RecordingFeedbackWidget(
              elapsedTime: elapsedTime,
              remainingTime: remainingTime,
              progress: progress,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('5s remaining'), findsOneWidget);
    });

    testWidgets('shows good face detection feedback',
        (WidgetTester tester) async {
      // Arrange
      final goodFaceResult = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 85,
        timestamp: DateTime.now(),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RecordingFeedbackWidget(
              elapsedTime: const Duration(seconds: 2),
              remainingTime: const Duration(seconds: 7),
              progress: 0.22,
              currentDetection: goodFaceResult,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Perfect! Keep this position'), findsOneWidget);
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });

    testWidgets('shows poor face detection feedback',
        (WidgetTester tester) async {
      // Arrange
      final poorFaceResult = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 60,
        boundingBox: const Rect.fromLTWH(100, 100, 150, 150),
        timestamp: DateTime.now(),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RecordingFeedbackWidget(
              remainingTime: 3,
              isRecording: true,
              faceResult: poorFaceResult,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Face detected - Move closer'), findsOneWidget);
      expect(find.byIcon(Icons.warning), findsOneWidget);

      // Check for orange color indicator
      final iconFinder = find.byIcon(Icons.warning);
      final icon = tester.widget<Icon>(iconFinder);
      expect(icon.color, equals(Colors.orange));
    });

    testWidgets('shows no face detected feedback', (WidgetTester tester) async {
      // Arrange
      final noFaceResult = FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0,
        timestamp: DateTime.now(),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RecordingFeedbackWidget(
              remainingTime: 7,
              isRecording: true,
              faceResult: noFaceResult,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('No face detected'), findsOneWidget);
      expect(find.byIcon(Icons.face), findsOneWidget);

      // Check for red color indicator
      final iconFinder = find.byIcon(Icons.face);
      final icon = tester.widget<Icon>(iconFinder);
      expect(icon.color, equals(Colors.red));
    });

    testWidgets('shows multiple faces detected feedback',
        (WidgetTester tester) async {
      // Arrange
      final multipleFacesResult = FaceDetectionResult(
        faceDetected: true,
        faceCount: 2,
        coveragePercentage: 75,
        boundingBox: const Rect.fromLTWH(100, 100, 200, 200),
        timestamp: DateTime.now(),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RecordingFeedbackWidget(
              remainingTime: 4,
              isRecording: true,
              faceResult: multipleFacesResult,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Multiple faces detected'), findsOneWidget);
      expect(find.byIcon(Icons.groups), findsOneWidget);

      // Check for orange color indicator
      final iconFinder = find.byIcon(Icons.groups);
      final icon = tester.widget<Icon>(iconFinder);
      expect(icon.color, equals(Colors.orange));
    });

    testWidgets('displays recording indicator when recording',
        (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: RecordingFeedbackWidget(
              remainingTime: 8,
              isRecording: true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.fiber_manual_record), findsOneWidget);

      final recordingIcon =
          tester.widget<Icon>(find.byIcon(Icons.fiber_manual_record));
      expect(recordingIcon.color, equals(Colors.red));
    });

    testWidgets('hides recording indicator when not recording',
        (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: RecordingFeedbackWidget(
              remainingTime: 0,
              isRecording: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.fiber_manual_record), findsNothing);
    });

    testWidgets('displays correct time format for different values',
        (WidgetTester tester) async {
      // Test 9 seconds
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: RecordingFeedbackWidget(
              remainingTime: 9,
              isRecording: true,
            ),
          ),
        ),
      );
      expect(find.text('9s'), findsOneWidget);

      // Test 0 seconds
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: RecordingFeedbackWidget(
              remainingTime: 0,
              isRecording: false,
            ),
          ),
        ),
      );
      expect(find.text('0s'), findsOneWidget);
    });

    testWidgets('has proper layout structure', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: RecordingFeedbackWidget(
              remainingTime: 5,
              isRecording: true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(Container), findsWidgets);
      expect(find.byType(Row), findsOneWidget);
      expect(find.byType(Column), findsOneWidget);
    });

    testWidgets('has correct accessibility semantics',
        (WidgetTester tester) async {
      // Arrange
      final faceResult = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 85,
        boundingBox: const Rect.fromLTWH(100, 100, 200, 200),
        timestamp: DateTime.now(),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RecordingFeedbackWidget(
              remainingTime: 5,
              isRecording: true,
              faceResult: faceResult,
            ),
          ),
        ),
      );

      // Assert
      expect(
        find.bySemanticsLabel('Recording in progress, 5 seconds remaining'),
        findsOneWidget,
      );
      expect(
        find.bySemanticsLabel('Face detected with good coverage'),
        findsOneWidget,
      );
    });

    testWidgets('updates accessibility for different states',
        (WidgetTester tester) async {
      // Test no face detected accessibility
      final noFaceResult = FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0,
        timestamp: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RecordingFeedbackWidget(
              remainingTime: 3,
              isRecording: true,
              faceResult: noFaceResult,
            ),
          ),
        ),
      );

      expect(
        find.bySemanticsLabel(
          'No face detected, please position your face in the guide',
        ),
        findsOneWidget,
      );
    });
  });
}
