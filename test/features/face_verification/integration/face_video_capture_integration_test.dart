import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:bloomg_flutter/features/face_verification/view/face_video_capture_page.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/core/services/logger_service.dart';

import 'face_video_capture_integration_test.mocks.dart';

@GenerateMocks([
  FaceDetectionRepository,
  VideoStorageRepository,
  LoggerService,
])
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Face Video Capture Integration Tests', () {
    late MockFaceDetectionRepository mockFaceDetectionRepository;
    late MockVideoStorageRepository mockVideoStorageRepository;
    late MockLoggerService mockLoggerService;

    setUp(() {
      mockFaceDetectionRepository = MockFaceDetectionRepository();
      mockVideoStorageRepository = MockVideoStorageRepository();
      mockLoggerService = MockLoggerService();
    });

    Widget createTestApp() {
      return MaterialApp(
        home: BlocProvider(
          create: (context) => FaceVideoCaptureBloc(
            faceDetectionRepository: mockFaceDetectionRepository,
            videoStorageRepository: mockVideoStorageRepository,
            logger: mockLoggerService,
          ),
          child: const FaceVideoCapturePage(),
        ),
      );
    }

    testWidgets('complete successful video capture workflow',
        (WidgetTester tester) async {
      // Arrange
      when(mockFaceDetectionRepository.initialize())
          .thenAnswer((_) async => {});

      final goodFaceResult = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 85,
        boundingBox: const Rect.fromLTWH(100, 100, 200, 200),
        timestamp: DateTime.now(),
      );

      when(mockFaceDetectionRepository.processFrame(any))
          .thenAnswer((_) async => goodFaceResult);

      const successStats = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 95,
        framesWithGoodCoverage: 85,
        averageCoverage: 82.5,
        minCoverage: 75.0,
        maxCoverage: 90.0,
      );

      when(mockVideoStorageRepository.saveVideo(any))
          .thenAnswer((_) async => '/path/to/video.mp4');
      when(mockFaceDetectionRepository.validateRecording(any))
          .thenAnswer((_) async => successStats);

      // Act
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Verify initial state
      expect(find.text('Position your face in the guide'), findsOneWidget);
      expect(find.text('Start Recording'), findsOneWidget);

      // Start recording
      await tester.tap(find.text('Start Recording'));
      await tester.pump();

      // Verify countdown starts
      expect(find.text('3'), findsOneWidget);
      await tester.pump(const Duration(seconds: 1));
      expect(find.text('2'), findsOneWidget);
      await tester.pump(const Duration(seconds: 1));
      expect(find.text('1'), findsOneWidget);
      await tester.pump(const Duration(seconds: 1));

      // Verify recording starts
      expect(find.byIcon(Icons.fiber_manual_record), findsOneWidget);
      expect(find.text('9s'), findsOneWidget);

      // Wait for recording to complete
      await tester.pump(const Duration(seconds: 9));

      // Verify processing state
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Processing video...'), findsOneWidget);

      await tester.pumpAndSettle();

      // Verify success state
      expect(find.text('Recording Successful!'), findsOneWidget);
      expect(find.text('Face coverage: 82.5%'), findsOneWidget);
      expect(find.text('Record Again'), findsOneWidget);
      expect(find.text('Continue'), findsOneWidget);

      // Verify repository calls
      verify(mockFaceDetectionRepository.initialize()).called(1);
      verify(mockVideoStorageRepository.saveVideo(any)).called(1);
      verify(mockFaceDetectionRepository.validateRecording(any)).called(1);
    });

    testWidgets('handles camera initialization failure',
        (WidgetTester tester) async {
      // Arrange
      when(mockFaceDetectionRepository.initialize())
          .thenThrow(Exception('Camera permission denied'));

      // Act
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Camera Error'), findsOneWidget);
      expect(find.text('Failed to initialize camera'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('handles poor face coverage failure',
        (WidgetTester tester) async {
      // Arrange
      when(mockFaceDetectionRepository.initialize())
          .thenAnswer((_) async => {});

      final poorFaceResult = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 60,
        boundingBox: const Rect.fromLTWH(100, 100, 150, 150),
        timestamp: DateTime.now(),
      );

      when(mockFaceDetectionRepository.processFrame(any))
          .thenAnswer((_) async => poorFaceResult);

      const failureStats = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 60,
        framesWithGoodCoverage: 40,
        averageCoverage: 65,
        minCoverage: 30.0,
        maxCoverage: 85.0,
      );

      when(mockVideoStorageRepository.saveVideo(any))
          .thenAnswer((_) async => '/path/to/video.mp4');
      when(mockFaceDetectionRepository.validateRecording(any))
          .thenAnswer((_) async => failureStats);

      // Act
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Start and complete recording
      await tester.tap(find.text('Start Recording'));
      await tester.pump(const Duration(seconds: 12)); // Countdown + recording
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Recording Failed'), findsOneWidget);
      expect(find.text('Insufficient face coverage'), findsOneWidget);
      expect(find.text('Try Again'), findsOneWidget);
    });

    testWidgets('reset functionality works correctly',
        (WidgetTester tester) async {
      // Arrange
      when(mockFaceDetectionRepository.initialize())
          .thenAnswer((_) async => {});

      const successStats = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 95,
        framesWithGoodCoverage: 85,
        averageCoverage: 82.5,
        minCoverage: 75.0,
        maxCoverage: 90.0,
      );

      when(mockVideoStorageRepository.saveVideo(any))
          .thenAnswer((_) async => '/path/to/video.mp4');
      when(mockFaceDetectionRepository.validateRecording(any))
          .thenAnswer((_) async => successStats);

      // Act
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Complete a successful recording
      await tester.tap(find.text('Start Recording'));
      await tester.pump(const Duration(seconds: 12));
      await tester.pumpAndSettle();

      // Verify success state
      expect(find.text('Recording Successful!'), findsOneWidget);

      // Reset
      await tester.tap(find.text('Record Again'));
      await tester.pumpAndSettle();

      // Verify back to initial state
      expect(find.text('Position your face in the guide'), findsOneWidget);
      expect(find.text('Start Recording'), findsOneWidget);
    });

    testWidgets('handles no face detected during recording',
        (WidgetTester tester) async {
      // Arrange
      when(mockFaceDetectionRepository.initialize())
          .thenAnswer((_) async => {});

      final noFaceResult = FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0,
        timestamp: DateTime.now(),
      );

      when(mockFaceDetectionRepository.processFrame(any))
          .thenAnswer((_) async => noFaceResult);

      // Act
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Start recording
      await tester.tap(find.text('Start Recording'));
      await tester.pump(const Duration(seconds: 4)); // Skip countdown

      // Verify no face feedback
      expect(find.text('No face detected'), findsOneWidget);
      expect(find.byIcon(Icons.face), findsOneWidget);
    });

    testWidgets('handles multiple faces detected', (WidgetTester tester) async {
      // Arrange
      when(mockFaceDetectionRepository.initialize())
          .thenAnswer((_) async => {});

      final multipleFacesResult = FaceDetectionResult(
        faceDetected: true,
        faceCount: 2,
        coveragePercentage: 75,
        boundingBox: const Rect.fromLTWH(100, 100, 200, 200),
        timestamp: DateTime.now(),
      );

      when(mockFaceDetectionRepository.processFrame(any))
          .thenAnswer((_) async => multipleFacesResult);

      // Act
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Start recording
      await tester.tap(find.text('Start Recording'));
      await tester.pump(const Duration(seconds: 4)); // Skip countdown

      // Verify multiple faces feedback
      expect(find.text('Multiple faces detected'), findsOneWidget);
      expect(find.byIcon(Icons.groups), findsOneWidget);
    });

    testWidgets('accessibility features work correctly',
        (WidgetTester tester) async {
      // Arrange
      when(mockFaceDetectionRepository.initialize())
          .thenAnswer((_) async => {});

      // Act
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Assert accessibility labels
      expect(find.bySemanticsLabel('Start face verification recording'),
          findsOneWidget);
      expect(find.bySemanticsLabel('Face guide overlay'), findsOneWidget);
    });
  });
}
