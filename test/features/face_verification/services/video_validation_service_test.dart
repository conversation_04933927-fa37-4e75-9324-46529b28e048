import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VideoValidationService', () {
    late VideoValidationService videoValidationService;

    setUp(() {
      videoValidationService = VideoValidationService();
    });

    group('validateVideo', () {
      test('should return failure when video file does not exist', () async {
        // Arrange
        const videoPath = '/non/existent/path.mp4';
        final detectionResults = <FaceDetectionResult>[];
        const recordingDuration = Duration(seconds: 9);

        // Act
        final result = await videoValidationService.validateVideo(
          videoPath: videoPath,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        // Assert
        expect(result.isValid, isFalse);
        expect(result.reason, contains('Video file not found'));
        expect(result.videoPath, equals(videoPath));
      });

      test('should return failure when video file is empty', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync();
        final videoFile = File('${tempDir.path}/empty_video.mp4');
        await videoFile.create(); // Create empty file

        final detectionResults = <FaceDetectionResult>[];
        const recordingDuration = Duration(seconds: 9);

        // Act
        final result = await videoValidationService.validateVideo(
          videoPath: videoFile.path,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        // Assert
        expect(result.isValid, isFalse);
        expect(result.reason, contains('Video file is empty'));

        // Cleanup
        await tempDir.delete(recursive: true);
      });

      test('should return success for good detection results', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync();
        final videoFile = File('${tempDir.path}/test_video.mp4');
        await videoFile.writeAsBytes([1, 2, 3, 4]); // Create non-empty file

        final detectionResults = List.generate(100, (index) {
          return FaceDetectionResult(
            faceDetected: true,
            faceCount: 1,
            coveragePercentage: 85,
            boundingBox: const FaceBoundingBox(
              left: 100,
              top: 100,
              width: 200,
              height: 200,
            ),
            timestamp: DateTime.now(),
          );
        });
        const recordingDuration = Duration(seconds: 9);

        // Act
        final result = await videoValidationService.validateVideo(
          videoPath: videoFile.path,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        // Assert
        expect(result.isValid, isTrue);
        expect(result.qualityScore, greaterThan(80.0));
        expect(result.coverageStats.totalFrames, equals(100));
        expect(result.coverageStats.framesWithFace, equals(100));

        // Cleanup
        await tempDir.delete(recursive: true);
      });

      test('should return failure for poor detection results', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync();
        final videoFile = File('${tempDir.path}/poor_video.mp4');
        await videoFile.writeAsBytes([1, 2, 3, 4]); // Create non-empty file

        final detectionResults = List.generate(50, (index) {
          return FaceDetectionResult(
            faceDetected: index % 3 == 0, // Only 1/3 have faces
            faceCount: index % 3 == 0 ? 1 : 0,
            coveragePercentage: index % 3 == 0 ? 60.0 : 0.0, // Poor coverage
            boundingBox: index % 3 == 0
                ? const FaceBoundingBox(
                    left: 100,
                    top: 100,
                    width: 150,
                    height: 150,
                  )
                : null,
            timestamp: DateTime.now(),
          );
        });
        const recordingDuration = Duration(seconds: 9);

        // Act
        final result = await videoValidationService.validateVideo(
          videoPath: videoFile.path,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        // Assert
        expect(result.isValid, isFalse);
        expect(result.qualityScore, lessThan(70.0));
        expect(result.reason, contains('Validation failed'));

        // Cleanup
        await tempDir.delete(recursive: true);
      });

      test('should return failure for too short recording', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync();
        final videoFile = File('${tempDir.path}/short_video.mp4');
        await videoFile.writeAsBytes([1, 2, 3, 4]); // Create non-empty file

        final detectionResults = List.generate(30, (index) {
          return FaceDetectionResult(
            faceDetected: true,
            faceCount: 1,
            coveragePercentage: 85,
            boundingBox: const FaceBoundingBox(
              left: 100,
              top: 100,
              width: 200,
              height: 200,
            ),
            timestamp: DateTime.now(),
          );
        });
        const recordingDuration = Duration(seconds: 5); // Too short

        // Act
        final result = await videoValidationService.validateVideo(
          videoPath: videoFile.path,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        // Assert
        expect(result.isValid, isFalse);
        expect(result.reason, contains('Recording too short'));

        // Cleanup
        await tempDir.delete(recursive: true);
      });
    });

    group('VideoValidationResult', () {
      test('should create failure result correctly', () {
        // Act
        const result = VideoValidationResult.failure(
          reason: 'Test failure',
          videoPath: '/test/path.mp4',
        );

        // Assert
        expect(result.isValid, isFalse);
        expect(result.reason, equals('Test failure'));
        expect(result.videoPath, equals('/test/path.mp4'));
        expect(result.qualityScore, equals(0.0));
      });

      test('should create success result correctly', () {
        // Act
        const result = VideoValidationResult(
          isValid: true,
          reason: 'Test success',
          videoPath: '/test/path.mp4',
          qualityScore: 85.5,
        );

        // Assert
        expect(result.isValid, isTrue);
        expect(result.reason, equals('Test success'));
        expect(result.videoPath, equals('/test/path.mp4'));
        expect(result.qualityScore, equals(85.5));
      });

      test('should have proper toString representation', () {
        // Act
        const result = VideoValidationResult(
          isValid: true,
          reason: 'Test success',
          videoPath: '/test/path.mp4',
          qualityScore: 85.5,
        );

        // Assert
        final stringRepresentation = result.toString();
        expect(stringRepresentation, contains('isValid: true'));
        expect(stringRepresentation, contains('qualityScore: 85.5'));
        expect(stringRepresentation, contains('reason: Test success'));
      });
    });
  });
}
