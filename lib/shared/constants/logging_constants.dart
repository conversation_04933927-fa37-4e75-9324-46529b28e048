/// {@template logging_constants}
/// Constants for consistent logging throughout the application.
/// {@endtemplate}
class LoggingConstants {
  /// Private constructor to prevent instantiation
  const LoggingConstants._();

  // Module identifiers
  static const String authModule = 'AUTH';
  static const String firebaseModule = 'FIREBASE';
  static const String navigationModule = 'NAVIGATION';
  static const String formModule = 'FORM';
  static const String networkModule = 'NETWORK';
  static const String appModule = 'APP';
  static const String blocModule = 'BLOC';
  static const String faceVerificationModule = 'FACE_VERIFICATION';

  // Authentication actions
  static const String loginAttempt = 'Login attempt';
  static const String loginSuccess = 'Login successful';
  static const String loginFailure = 'Login failed';
  static const String signupAttempt = 'Signup attempt';
  static const String signupSuccess = 'Signup successful';
  static const String signupFailure = 'Signup failed';
  static const String passwordResetAttempt = 'Password reset attempt';
  static const String passwordResetSuccess = 'Password reset successful';
  static const String passwordResetFailure = 'Password reset failed';
  static const String logoutAttempt = 'Logout attempt';
  static const String logoutSuccess = 'Logout successful';
  static const String logoutFailure = 'Logout failed';

  // Firebase actions
  static const String firebaseInitialized = 'Firebase initialized';
  static const String firebaseInitializationFailed =
      'Firebase initialization failed';
  static const String firestoreRead = 'Firestore read operation';
  static const String firestoreWrite = 'Firestore write operation';
  static const String firestoreError = 'Firestore operation error';

  // Navigation actions
  static const String navigationPush = 'Navigation push';
  static const String navigationPop = 'Navigation pop';
  static const String navigationReplace = 'Navigation replace';
  static const String routeChanged = 'Route changed';

  // Form actions
  static const String formValidation = 'Form validation';
  static const String formSubmission = 'Form submission';
  static const String fieldChanged = 'Field changed';
  static const String fieldTouched = 'Field touched';
  static const String validationError = 'Validation error';

  // Network actions
  static const String networkRequest = 'Network request';
  static const String networkResponse = 'Network response';
  static const String networkError = 'Network error';
  static const String networkTimeout = 'Network timeout';

  // App lifecycle
  static const String appStarted = 'App started';
  static const String appPaused = 'App paused';
  static const String appResumed = 'App resumed';
  static const String appDetached = 'App detached';
  static const String appInactive = 'App inactive';

  // BLoC actions
  static const String blocStateChange = 'BLoC state change';
  static const String blocError = 'BLoC error';
  static const String blocEvent = 'BLoC event';

  // Error types
  static const String criticalError = 'Critical error';
  static const String recoverableError = 'Recoverable error';
  static const String validationFailure = 'Validation failure';
  static const String networkFailure = 'Network failure';
  static const String authenticationFailure = 'Authentication failure';

  // Helper methods for consistent log message formatting

  /// Formats a log message with module and action
  static String formatMessage(String module, String action, [String? details]) {
    final baseMessage = '[$module] $action';
    return details != null ? '$baseMessage: $details' : baseMessage;
  }

  /// Formats an error message with module and error type
  static String formatError(
    String module,
    String errorType,
    String error, [
    String? context,
  ]) {
    final baseMessage = '[$module] $errorType: $error';
    return context != null ? '$baseMessage (Context: $context)' : baseMessage;
  }

  /// Formats a user action message
  static String formatUserAction(
    String action,
    String userId, [
    Map<String, dynamic>? metadata,
  ]) {
    final baseMessage = '[USER] $action - User: $userId';
    if (metadata != null && metadata.isNotEmpty) {
      final metadataStr =
          metadata.entries.map((e) => '${e.key}: ${e.value}').join(', ');
      return '$baseMessage (Metadata: $metadataStr)';
    }
    return baseMessage;
  }

  /// Formats a performance message
  static String formatPerformance(
    String operation,
    Duration duration, [
    String? details,
  ]) {
    final baseMessage =
        '[PERFORMANCE] $operation completed in ${duration.inMilliseconds}ms';
    return details != null ? '$baseMessage - $details' : baseMessage;
  }

  /// Formats a security-related message
  static String formatSecurity(
    String event,
    String level, [
    String? details,
  ]) {
    final baseMessage = '[SECURITY] $level: $event';
    return details != null ? '$baseMessage - $details' : baseMessage;
  }
}
