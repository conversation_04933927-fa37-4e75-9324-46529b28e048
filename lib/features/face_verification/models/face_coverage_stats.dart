import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:equatable/equatable.dart';

/// {@template face_coverage_stats}
/// Statistics about face coverage throughout a video recording session.
/// {@endtemplate}
class FaceCoverageStats extends Equatable {
  /// {@macro face_coverage_stats}
  const FaceCoverageStats({
    required this.totalFrames,
    required this.framesWithFace,
    required this.framesWithValidCoverage,
    required this.averageCoverage,
    required this.minimumCoverage,
    required this.maximumCoverage,
    required this.recordingDuration,
    required this.detectionResults,
  });

  /// Creates a FaceCoverageStats from a JSON map
  factory FaceCoverageStats.fromJson(Map<String, dynamic> json) {
    return FaceCoverageStats(
      totalFrames: json['total_frames'] as int,
      framesWithFace: json['frames_with_face'] as int,
      framesWithValidCoverage: json['frames_with_valid_coverage'] as int,
      averageCoverage: (json['average_coverage'] as num).toDouble(),
      minimumCoverage: (json['minimum_coverage'] as num).toDouble(),
      maximumCoverage: (json['maximum_coverage'] as num).toDouble(),
      recordingDuration: Duration(
        milliseconds: json['recording_duration_ms'] as int,
      ),
      detectionResults: const [], // Not serialized in basic JSON
    );
  }

  /// Creates empty stats for initialization
  const FaceCoverageStats.empty()
      : totalFrames = 0,
        framesWithFace = 0,
        framesWithValidCoverage = 0,
        averageCoverage = 0.0,
        minimumCoverage = 0.0,
        maximumCoverage = 0.0,
        recordingDuration = Duration.zero,
        detectionResults = const [];

  /// Total number of frames analyzed
  final int totalFrames;

  /// Number of frames where a face was detected
  final int framesWithFace;

  /// Number of frames with valid face coverage (≥80%)
  final int framesWithValidCoverage;

  /// Average face coverage percentage across all frames
  final double averageCoverage;

  /// Minimum face coverage percentage recorded
  final double minimumCoverage;

  /// Maximum face coverage percentage recorded
  final double maximumCoverage;

  /// Total duration of the recording
  final Duration recordingDuration;

  /// List of all detection results
  final List<FaceDetectionResult> detectionResults;

  /// Percentage of frames with face detected (0-100)
  double get faceDetectionRate =>
      totalFrames > 0 ? (framesWithFace / totalFrames) * 100 : 0.0;

  /// Percentage of frames with valid coverage (0-100)
  double get validCoverageRate =>
      totalFrames > 0 ? (framesWithValidCoverage / totalFrames) * 100 : 0.0;

  /// Whether the recording meets the quality threshold
  /// (at least 70% of frames have valid face coverage)
  bool get meetsQualityThreshold => validCoverageRate >= 70.0;

  /// Quality score based on various factors (0-100)
  double get qualityScore {
    if (totalFrames == 0) return 0;

    // Weight different factors
    const faceDetectionWeight = 0.3;
    const validCoverageWeight = 0.5;
    const averageCoverageWeight = 0.2;

    final faceDetectionScore = faceDetectionRate;
    final validCoverageScore = validCoverageRate;
    final averageCoverageScore = (averageCoverage / 100) * 100;

    return (faceDetectionScore * faceDetectionWeight) +
        (validCoverageScore * validCoverageWeight) +
        (averageCoverageScore * averageCoverageWeight);
  }

  /// Creates a new instance with updated statistics
  FaceCoverageStats copyWith({
    int? totalFrames,
    int? framesWithFace,
    int? framesWithValidCoverage,
    double? averageCoverage,
    double? minimumCoverage,
    double? maximumCoverage,
    Duration? recordingDuration,
    List<FaceDetectionResult>? detectionResults,
  }) {
    return FaceCoverageStats(
      totalFrames: totalFrames ?? this.totalFrames,
      framesWithFace: framesWithFace ?? this.framesWithFace,
      framesWithValidCoverage:
          framesWithValidCoverage ?? this.framesWithValidCoverage,
      averageCoverage: averageCoverage ?? this.averageCoverage,
      minimumCoverage: minimumCoverage ?? this.minimumCoverage,
      maximumCoverage: maximumCoverage ?? this.maximumCoverage,
      recordingDuration: recordingDuration ?? this.recordingDuration,
      detectionResults: detectionResults ?? this.detectionResults,
    );
  }

  @override
  List<Object> get props => [
        totalFrames,
        framesWithFace,
        framesWithValidCoverage,
        averageCoverage,
        minimumCoverage,
        maximumCoverage,
        recordingDuration,
        detectionResults,
      ];

  /// Converts the stats to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'total_frames': totalFrames,
      'frames_with_face': framesWithFace,
      'frames_with_valid_coverage': framesWithValidCoverage,
      'average_coverage': averageCoverage,
      'minimum_coverage': minimumCoverage,
      'maximum_coverage': maximumCoverage,
      'recording_duration_ms': recordingDuration.inMilliseconds,
      'face_detection_rate': faceDetectionRate,
      'valid_coverage_rate': validCoverageRate,
      'quality_score': qualityScore,
      'meets_quality_threshold': meetsQualityThreshold,
    };
  }

  @override
  String toString() {
    return 'FaceCoverageStats('
        'totalFrames: $totalFrames, '
        'framesWithFace: $framesWithFace, '
        'framesWithValidCoverage: $framesWithValidCoverage, '
        'averageCoverage: ${averageCoverage.toStringAsFixed(1)}%, '
        'faceDetectionRate: ${faceDetectionRate.toStringAsFixed(1)}%, '
        'validCoverageRate: ${validCoverageRate.toStringAsFixed(1)}%, '
        'qualityScore: ${qualityScore.toStringAsFixed(1)}, '
        'meetsQualityThreshold: $meetsQualityThreshold'
        ')';
  }
}
