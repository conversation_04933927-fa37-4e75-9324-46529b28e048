import 'dart:math';

import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';

/// {@template face_detection_utils}
/// Utility functions for face detection operations and calculations.
/// {@endtemplate}
class FaceDetectionUtils {
  /// Private constructor to prevent instantiation
  const FaceDetectionUtils._();

  /// Face guide configuration constants
  static const double faceGuideWidthRatio = 0.7; // 70% of screen width
  static const double faceGuideHeightRatio = 0.8; // 80% of screen height
  static const double minimumCoverageThreshold = 80; // 80% coverage required

  /// Calculates the face guide area bounds for a given screen size
  static FaceBoundingBox calculateFaceGuideArea(
      double screenWidth, double screenHeight) {
    final guideWidth = screenWidth * faceGuideWidthRatio;
    final guideHeight = screenHeight * faceGuideHeightRatio;
    final left = (screenWidth - guideWidth) / 2;
    final top = (screenHeight - guideHeight) / 2;

    return FaceBoundingBox(
      left: left,
      top: top,
      width: guideWidth,
      height: guideHeight,
    );
  }

  /// Calculates the intersection area between two bounding boxes
  static double calculateIntersectionArea(
    FaceBoundingBox box1,
    FaceBoundingBox box2,
  ) {
    final left = max(box1.left, box2.left);
    final top = max(box1.top, box2.top);
    final right = min(box1.right, box2.right);
    final bottom = min(box1.bottom, box2.bottom);

    if (left >= right || top >= bottom) {
      return 0; // No intersection
    }

    return (right - left) * (bottom - top);
  }

  /// Calculates face coverage percentage within the guide area
  static double calculateFaceCoverage(
    FaceBoundingBox faceBounds,
    double screenWidth,
    double screenHeight,
  ) {
    final guideArea = calculateFaceGuideArea(screenWidth, screenHeight);
    final intersectionArea = calculateIntersectionArea(faceBounds, guideArea);
    final guideAreaSize = guideArea.width * guideArea.height;

    if (guideAreaSize == 0) return 0;

    return (intersectionArea / guideAreaSize) * 100.0;
  }

  /// Determines if a face detection result meets quality requirements
  static bool meetsQualityRequirements(FaceDetectionResult result) {
    return result.faceDetected &&
        result.faceCount == 1 &&
        result.coveragePercentage >= minimumCoverageThreshold &&
        (result.confidence ?? 0.0) >= 0.7;
  }

  /// Calculates the center point of a face bounding box
  static Point<double> calculateFaceCenter(FaceBoundingBox bounds) {
    return Point(bounds.centerX, bounds.centerY);
  }

  /// Calculates the distance between two face centers
  static double calculateFaceDistance(
    FaceBoundingBox face1,
    FaceBoundingBox face2,
  ) {
    final center1 = calculateFaceCenter(face1);
    final center2 = calculateFaceCenter(face2);

    final dx = center1.x - center2.x;
    final dy = center1.y - center2.y;

    return sqrt(dx * dx + dy * dy);
  }

  /// Determines if a face is centered within the guide area
  static bool isFaceCentered(
    FaceBoundingBox faceBounds,
    double screenWidth,
    double screenHeight, {
    double tolerance = 50.0,
  }) {
    final guideArea = calculateFaceGuideArea(screenWidth, screenHeight);
    final faceCenter = calculateFaceCenter(faceBounds);
    final guideCenter = calculateFaceCenter(guideArea);

    final distance = sqrt(
      pow(faceCenter.x - guideCenter.x, 2) +
          pow(faceCenter.y - guideCenter.y, 2),
    );

    return distance <= tolerance;
  }

  /// Calculates face size relative to the guide area
  static double calculateRelativeFaceSize(
    FaceBoundingBox faceBounds,
    double screenWidth,
    double screenHeight,
  ) {
    final guideArea = calculateFaceGuideArea(screenWidth, screenHeight);
    final faceArea = faceBounds.width * faceBounds.height;
    final guideAreaSize = guideArea.width * guideArea.height;

    if (guideAreaSize == 0) return 0;

    return (faceArea / guideAreaSize) * 100.0;
  }

  /// Determines if face size is appropriate (not too small or too large)
  static bool isAppropriateSize(
    FaceBoundingBox faceBounds,
    double screenWidth,
    double screenHeight, {
    double minSizePercentage = 30.0,
    double maxSizePercentage = 90.0,
  }) {
    final relativeSize = calculateRelativeFaceSize(
      faceBounds,
      screenWidth,
      screenHeight,
    );

    return relativeSize >= minSizePercentage &&
        relativeSize <= maxSizePercentage;
  }

  /// Analyzes face detection stability over multiple frames
  static FaceStabilityAnalysis analyzeFaceStability(
    List<FaceDetectionResult> recentResults, {
    int maxFramesToAnalyze = 10,
  }) {
    if (recentResults.isEmpty) {
      return const FaceStabilityAnalysis(
        isStable: false,
        averageConfidence: 0,
        positionVariance: 0,
        sizeVariance: 0,
      );
    }

    final framesToAnalyze = recentResults.length > maxFramesToAnalyze
        ? recentResults.sublist(recentResults.length - maxFramesToAnalyze)
        : recentResults;

    final validResults = framesToAnalyze
        .where((result) => result.faceDetected && result.boundingBox != null)
        .toList();

    if (validResults.isEmpty) {
      return const FaceStabilityAnalysis(
        isStable: false,
        averageConfidence: 0,
        positionVariance: 0,
        sizeVariance: 0,
      );
    }

    // Calculate average confidence
    final averageConfidence = validResults
            .map((result) => result.confidence ?? 0.0)
            .reduce((a, b) => a + b) /
        validResults.length;

    // Calculate position variance
    final centers = validResults
        .map((result) => calculateFaceCenter(result.boundingBox!))
        .toList();

    final avgX =
        centers.map((p) => p.x).reduce((a, b) => a + b) / centers.length;
    final avgY =
        centers.map((p) => p.y).reduce((a, b) => a + b) / centers.length;

    final positionVariance = centers
            .map((p) => pow(p.x - avgX, 2) + pow(p.y - avgY, 2))
            .reduce((a, b) => a + b) /
        centers.length;

    // Calculate size variance
    final sizes = validResults
        .map((result) => result.boundingBox!.width * result.boundingBox!.height)
        .toList();

    final avgSize = sizes.reduce((a, b) => a + b) / sizes.length;
    final sizeVariance =
        sizes.map((size) => pow(size - avgSize, 2)).reduce((a, b) => a + b) /
            sizes.length;

    // Determine stability
    const maxPositionVariance = 2500.0; // pixels²
    const maxSizeVariance = 10000.0; // pixels²
    const minConfidence = 0.7;

    final isStable = positionVariance <= maxPositionVariance &&
        sizeVariance <= maxSizeVariance &&
        averageConfidence >= minConfidence;

    return FaceStabilityAnalysis(
      isStable: isStable,
      averageConfidence: averageConfidence,
      positionVariance: positionVariance,
      sizeVariance: sizeVariance,
    );
  }

  /// Provides feedback for face positioning
  static FacePositionFeedback getFacePositionFeedback(
    FaceDetectionResult result,
    double screenWidth,
    double screenHeight,
  ) {
    if (!result.faceDetected || result.boundingBox == null) {
      return const FacePositionFeedback(
        message: 'No face detected. Please position your face in the center.',
        feedbackType: FeedbackType.noFace,
      );
    }

    final faceBounds = result.boundingBox!;

    // Check if face is centered
    if (!isFaceCentered(faceBounds, screenWidth, screenHeight)) {
      return const FacePositionFeedback(
        message: 'Please center your face in the guide.',
        feedbackType: FeedbackType.notCentered,
      );
    }

    // Check face size
    if (!isAppropriateSize(faceBounds, screenWidth, screenHeight)) {
      final relativeSize = calculateRelativeFaceSize(
        faceBounds,
        screenWidth,
        screenHeight,
      );

      if (relativeSize < 30.0) {
        return const FacePositionFeedback(
          message: 'Move closer to the camera.',
          feedbackType: FeedbackType.tooFar,
        );
      } else {
        return const FacePositionFeedback(
          message: 'Move further from the camera.',
          feedbackType: FeedbackType.tooClose,
        );
      }
    }

    // Check coverage
    if (!result.meetsThreshold) {
      return FacePositionFeedback(
        message:
            'Face coverage: ${result.coveragePercentage.toStringAsFixed(0)}%. Need 80%+',
        feedbackType: FeedbackType.insufficientCoverage,
      );
    }

    return const FacePositionFeedback(
      message: 'Perfect! Keep this position.',
      feedbackType: FeedbackType.perfect,
    );
  }
}

/// {@template face_stability_analysis}
/// Analysis of face detection stability over multiple frames.
/// {@endtemplate}
class FaceStabilityAnalysis {
  /// {@macro face_stability_analysis}
  const FaceStabilityAnalysis({
    required this.isStable,
    required this.averageConfidence,
    required this.positionVariance,
    required this.sizeVariance,
  });

  /// Whether the face detection is stable
  final bool isStable;

  /// Average confidence across analyzed frames
  final double averageConfidence;

  /// Variance in face position
  final double positionVariance;

  /// Variance in face size
  final double sizeVariance;
}

/// {@template face_position_feedback}
/// Feedback for face positioning during recording.
/// {@endtemplate}
class FacePositionFeedback {
  /// {@macro face_position_feedback}
  const FacePositionFeedback({
    required this.message,
    required this.feedbackType,
  });

  /// Feedback message to display to user
  final String message;

  /// Type of feedback
  final FeedbackType feedbackType;
}

/// {@template feedback_type}
/// Types of face position feedback.
/// {@endtemplate}
enum FeedbackType {
  /// No face detected
  noFace,

  /// Face not centered in guide
  notCentered,

  /// Face too close to camera
  tooClose,

  /// Face too far from camera
  tooFar,

  /// Insufficient face coverage
  insufficientCoverage,

  /// Perfect positioning
  perfect,
}
