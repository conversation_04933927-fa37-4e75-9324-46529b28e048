import 'package:flutter/material.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';

/// {@template result_screen_widget}
/// Widget that displays the result of face verification recording.
///
/// Shows success or failure status with detailed statistics,
/// quality metrics, and action buttons.
/// {@endtemplate}
class ResultScreenWidget extends StatefulWidget {
  /// {@macro result_screen_widget}
  const ResultScreenWidget({
    required this.isSuccess,
    required this.title,
    required this.message,
    required this.coverageStats,
    required this.onRetry,
    super.key,
    this.onContinue,
  });

  /// Whether the verification was successful
  final bool isSuccess;

  /// Title to display
  final String title;

  /// Message to display
  final String message;

  /// Coverage statistics from the recording
  final FaceCoverageStats coverageStats;

  /// Callback for retry action
  final VoidCallback onRetry;

  /// Callback for continue action (null if not available)
  final VoidCallback? onContinue;

  @override
  State<ResultScreenWidget> createState() => _ResultScreenWidgetState();
}

class _ResultScreenWidgetState extends State<ResultScreenWidget>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _fadeController,
        curve: Curves.easeInOut,
      ),
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _scaleController,
        curve: Curves.elasticOut,
      ),
    );

    // Start animations
    _fadeController.forward();
    _scaleController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black,
            if (widget.isSuccess)
              Colors.green.withOpacity(0.1)
            else
              Colors.red.withOpacity(0.1),
            Colors.black,
          ],
        ),
      ),
      child: SafeArea(
        child: AnimatedBuilder(
          animation: Listenable.merge([_fadeAnimation, _scaleAnimation]),
          builder: (context, child) {
            return Opacity(
              opacity: _fadeAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: _buildContent(context),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          const Spacer(),

          // Result icon and title
          _buildResultHeader(),

          const SizedBox(height: 24),

          // Message
          _buildMessage(),

          const SizedBox(height: 32),

          // Statistics
          _buildStatistics(),

          const Spacer(),

          // Action buttons
          _buildActionButtons(context),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildResultHeader() {
    return Column(
      children: [
        // Result icon
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: widget.isSuccess
                ? Colors.green.withOpacity(0.2)
                : Colors.red.withOpacity(0.2),
            border: Border.all(
              color: widget.isSuccess ? Colors.green : Colors.red,
              width: 3,
            ),
          ),
          child: Icon(
            widget.isSuccess ? Icons.check : Icons.close,
            size: 60,
            color: widget.isSuccess ? Colors.green : Colors.red,
          ),
        ),

        const SizedBox(height: 16),

        // Title
        Text(
          widget.title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
        ),
      ),
      child: Text(
        widget.message,
        style: const TextStyle(
          color: Colors.white70,
          fontSize: 16,
          height: 1.4,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildStatistics() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Recording Statistics',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          // Quality score
          _buildStatItem(
            'Quality Score',
            '${widget.coverageStats.qualityScore.toStringAsFixed(1)}%',
            _getQualityScoreColor(widget.coverageStats.qualityScore),
          ),

          const SizedBox(height: 12),

          // Face detection rate
          _buildStatItem(
            'Face Detection Rate',
            '${widget.coverageStats.faceDetectionRate.toStringAsFixed(1)}%',
            _getDetectionRateColor(widget.coverageStats.faceDetectionRate),
          ),

          const SizedBox(height: 12),

          // Valid coverage rate
          _buildStatItem(
            'Valid Coverage Rate',
            '${widget.coverageStats.validCoverageRate.toStringAsFixed(1)}%',
            _getCoverageRateColor(widget.coverageStats.validCoverageRate),
          ),

          const SizedBox(height: 12),

          // Recording duration
          _buildStatItem(
            'Recording Duration',
            '${widget.coverageStats.recordingDuration.inSeconds}s',
            Colors.white70,
          ),

          const SizedBox(height: 12),

          // Total frames
          _buildStatItem(
            'Frames Analyzed',
            '${widget.coverageStats.totalFrames}',
            Colors.white70,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color valueColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 14,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: valueColor,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // Continue button (if available)
        if (widget.onContinue != null)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: widget.onContinue,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Continue',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

        if (widget.onContinue != null) const SizedBox(height: 12),

        // Retry button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: widget.onRetry,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Try Again',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getQualityScoreColor(double score) {
    if (score >= 80) {
      return Colors.green;
    } else if (score >= 60) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  Color _getDetectionRateColor(double rate) {
    if (rate >= 90) {
      return Colors.green;
    } else if (rate >= 70) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  Color _getCoverageRateColor(double rate) {
    if (rate >= 80) {
      return Colors.green;
    } else if (rate >= 60) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
