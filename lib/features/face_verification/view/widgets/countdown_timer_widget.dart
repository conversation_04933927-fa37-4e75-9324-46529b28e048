import 'package:flutter/material.dart';

/// {@template countdown_timer_widget}
/// Widget that displays an animated countdown timer.
///
/// Shows a large countdown number with scaling animation and
/// color changes as the countdown progresses.
/// {@endtemplate}
class CountdownTimerWidget extends StatefulWidget {
  /// {@macro countdown_timer_widget}
  const CountdownTimerWidget({
    required this.remainingSeconds,
    super.key,
  });

  /// Number of seconds remaining in the countdown
  final int remainingSeconds;

  @override
  State<CountdownTimerWidget> createState() => _CountdownTimerWidgetState();
}

class _CountdownTimerWidgetState extends State<CountdownTimerWidget>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  int _previousSeconds = 0;

  @override
  void initState() {
    super.initState();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.2,
    ).animate(
      CurvedAnimation(
        parent: _scaleController,
        curve: Curves.elasticOut,
      ),
    );

    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _fadeController,
        curve: Curves.easeInOut,
      ),
    );

    _previousSeconds = widget.remainingSeconds;
    _startAnimation();
  }

  @override
  void didUpdateWidget(CountdownTimerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.remainingSeconds != _previousSeconds) {
      _previousSeconds = widget.remainingSeconds;
      _startAnimation();
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _startAnimation() {
    _scaleController.reset();
    _fadeController.reset();

    _fadeController.forward();
    _scaleController.forward().then((_) {
      if (mounted) {
        _scaleController.animateTo(1);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black.withOpacity(0.3),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Countdown number
            AnimatedBuilder(
              animation: Listenable.merge([_scaleAnimation, _fadeAnimation]),
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _getCountdownColor().withOpacity(0.2),
                        border: Border.all(
                          color: _getCountdownColor(),
                          width: 4,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: _getCountdownColor().withOpacity(0.5),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          widget.remainingSeconds.toString(),
                          style: TextStyle(
                            fontSize: 80,
                            fontWeight: FontWeight.bold,
                            color: _getCountdownColor(),
                            shadows: [
                              Shadow(
                                color: Colors.black.withOpacity(0.5),
                                blurRadius: 10,
                                offset: const Offset(2, 2),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Countdown message
            AnimatedBuilder(
              animation: _fadeAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: _fadeAnimation.value,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      _getCountdownMessage(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 20),

            // Progress indicator
            AnimatedBuilder(
              animation: _fadeAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: _fadeAnimation.value * 0.7,
                  child: _buildProgressIndicator(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the circular progress indicator
  Widget _buildProgressIndicator() {
    const totalSeconds = 3; // Assuming 3-second countdown
    final progress = (totalSeconds - widget.remainingSeconds) / totalSeconds;

    return SizedBox(
      width: 60,
      height: 60,
      child: CircularProgressIndicator(
        value: progress,
        strokeWidth: 4,
        backgroundColor: Colors.white.withOpacity(0.3),
        valueColor: AlwaysStoppedAnimation<Color>(_getCountdownColor()),
      ),
    );
  }

  /// Gets the color for the countdown based on remaining time
  Color _getCountdownColor() {
    switch (widget.remainingSeconds) {
      case 3:
        return Colors.green;
      case 2:
        return Colors.orange;
      case 1:
        return Colors.red;
      default:
        return Colors.white;
    }
  }

  /// Gets the message to display during countdown
  String _getCountdownMessage() {
    switch (widget.remainingSeconds) {
      case 3:
        return 'Get ready...';
      case 2:
        return 'Position your face...';
      case 1:
        return 'Recording starts now!';
      default:
        return 'Starting...';
    }
  }
}
