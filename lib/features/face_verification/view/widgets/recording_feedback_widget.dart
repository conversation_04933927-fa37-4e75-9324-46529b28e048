import 'package:flutter/material.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';

/// {@template recording_feedback_widget}
/// Widget that displays recording progress and real-time face detection feedback.
///
/// Shows recording timer, progress bar, face detection status,
/// and visual feedback during video recording.
/// {@endtemplate}
class RecordingFeedbackWidget extends StatefulWidget {
  /// {@macro recording_feedback_widget}
  const RecordingFeedbackWidget({
    required this.elapsedTime,
    required this.remainingTime,
    required this.progress,
    super.key,
    this.currentDetection,
  });

  /// Time elapsed since recording started
  final Duration elapsedTime;

  /// Time remaining in the recording
  final Duration remainingTime;

  /// Recording progress (0.0 to 1.0)
  final double progress;

  /// Current face detection result
  final FaceDetectionResult? currentDetection;

  @override
  State<RecordingFeedbackWidget> createState() =>
      _RecordingFeedbackWidgetState();
}

class _RecordingFeedbackWidgetState extends State<RecordingFeedbackWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _recordingController;
  late Animation<double> _pulseAnimation;
  late Animation<Color?> _recordingColorAnimation;

  @override
  void initState() {
    super.initState();

    // Pulse animation for recording indicator
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _pulseController,
        curve: Curves.easeInOut,
      ),
    );

    // Recording color animation
    _recordingController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _recordingColorAnimation = ColorTween(
      begin: Colors.red,
      end: Colors.red.withOpacity(0.7),
    ).animate(
      CurvedAnimation(
        parent: _recordingController,
        curve: Curves.easeInOut,
      ),
    );

    _pulseController.repeat(reverse: true);
    _recordingController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _recordingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Recording indicator (top)
        _buildRecordingIndicator(),

        // Progress bar (top)
        _buildProgressBar(),

        // Face detection feedback (center)
        if (widget.currentDetection != null) _buildFaceDetectionFeedback(),

        // Timer display (bottom)
        _buildTimerDisplay(),
      ],
    );
  }

  /// Builds the recording indicator
  Widget _buildRecordingIndicator() {
    return Positioned(
      top: 60,
      left: 20,
      child: AnimatedBuilder(
        animation:
            Listenable.merge([_pulseAnimation, _recordingColorAnimation]),
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
              decoration: BoxDecoration(
                color: _recordingColorAnimation.value,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'REC',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Builds the progress bar
  Widget _buildProgressBar() {
    return Positioned(
      top: 100,
      left: 20,
      right: 20,
      child: Column(
        children: [
          // Progress bar
          Container(
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: widget.progress,
              child: Container(
                decoration: BoxDecoration(
                  color: _getProgressColor(),
                  borderRadius: BorderRadius.circular(2),
                  boxShadow: [
                    BoxShadow(
                      color: _getProgressColor().withOpacity(0.5),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Progress percentage
          Text(
            '${(widget.progress * 100).toInt()}%',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds face detection feedback
  Widget _buildFaceDetectionFeedback() {
    final detection = widget.currentDetection!;

    return Positioned(
      top: MediaQuery.of(context).size.height * 0.3,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: _getFeedbackBackgroundColor(detection).withOpacity(0.9),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getFeedbackBorderColor(detection),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Status icon and message
            Row(
              children: [
                Icon(
                  _getFeedbackIcon(detection),
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _getFeedbackMessage(detection),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Coverage meter
            _buildCoverageMeter(detection),
          ],
        ),
      ),
    );
  }

  /// Builds the coverage meter
  Widget _buildCoverageMeter(FaceDetectionResult detection) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Face Coverage',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
            Text(
              '${detection.coveragePercentage.toStringAsFixed(0)}%',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          height: 6,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.3),
            borderRadius: BorderRadius.circular(3),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: (detection.coveragePercentage / 100).clamp(0.0, 1.0),
            child: Container(
              decoration: BoxDecoration(
                color: _getCoverageColor(detection.coveragePercentage),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the timer display
  Widget _buildTimerDisplay() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 12,
          ),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.8),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
            ),
          ),
          child: Text(
            '${widget.remainingTime.inSeconds}s remaining',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  /// Gets progress bar color based on progress
  Color _getProgressColor() {
    if (widget.progress < 0.3) {
      return Colors.red;
    } else if (widget.progress < 0.7) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  /// Gets feedback background color based on detection quality
  Color _getFeedbackBackgroundColor(FaceDetectionResult detection) {
    if (!detection.faceDetected) {
      return Colors.red;
    } else if (detection.meetsThreshold) {
      return Colors.green;
    } else {
      return Colors.orange;
    }
  }

  /// Gets feedback border color based on detection quality
  Color _getFeedbackBorderColor(FaceDetectionResult detection) {
    return _getFeedbackBackgroundColor(detection).withOpacity(0.8);
  }

  /// Gets feedback icon based on detection quality
  IconData _getFeedbackIcon(FaceDetectionResult detection) {
    if (!detection.faceDetected) {
      return Icons.face_retouching_off;
    } else if (detection.meetsThreshold) {
      return Icons.check_circle;
    } else {
      return Icons.warning;
    }
  }

  /// Gets feedback message based on detection quality
  String _getFeedbackMessage(FaceDetectionResult detection) {
    if (!detection.faceDetected) {
      return 'No face detected';
    } else if (detection.faceCount > 1) {
      return 'Multiple faces detected';
    } else if (detection.meetsThreshold) {
      return 'Perfect! Keep this position';
    } else {
      return 'Adjust your position';
    }
  }

  /// Gets coverage color based on percentage
  Color _getCoverageColor(double percentage) {
    if (percentage >= 80) {
      return Colors.green;
    } else if (percentage >= 60) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
