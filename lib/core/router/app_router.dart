import 'dart:async';

import 'package:bloomg_flutter/auth/cubit/auth_cubit.dart';
import 'package:bloomg_flutter/auth/view/create_account_page.dart';
import 'package:bloomg_flutter/auth/view/forgot_password_page.dart';
import 'package:bloomg_flutter/auth/view/login_page.dart';
import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/features/face_verification/view/face_video_capture_page.dart';
import 'package:bloomg_flutter/home/<USER>/home_page.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// {@template router_refresh_notifier}
/// A [ChangeNotifier] that listens to [AuthCubit] state changes
/// and notifies [GoRouter] to refresh its routes.
/// {@endtemplate}
class RouterRefreshNotifier extends ChangeNotifier {
  /// {@macro router_refresh_notifier}
  RouterRefreshNotifier(this._authCubit) {
    _subscription = _authCubit.stream.listen((_) {
      notifyListeners();
    });
  }

  final AuthCubit _authCubit;
  late final StreamSubscription<AuthState> _subscription;

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}

/// {@template app_router}
/// Application router configuration using go_router.
/// {@endtemplate}
class AppRouter {
  /// {@macro app_router}
  AppRouter._();

  static final LoggerService _logger = LoggerService();

  /// Route paths
  static const String loginPath = '/login';
  static const String signupPath = '/signup';
  static const String forgotPasswordPath = '/forgot-password';
  static const String homePath = '/home';
  static const String faceVerificationPath = '/face-verification';

  /// Route names
  static const String loginRoute = 'login';
  static const String signupRoute = 'signup';
  static const String forgotPasswordRoute = 'forgot-password';
  static const String homeRoute = 'home';
  static const String faceVerificationRoute = 'face-verification';

  /// Creates and configures the router.
  static GoRouter createRouter() {
    final authCubit = getIt<AuthCubit>();
    final refreshNotifier = RouterRefreshNotifier(authCubit);

    return GoRouter(
      initialLocation: loginPath,
      debugLogDiagnostics: true,
      refreshListenable: refreshNotifier,
      redirect: _redirect,
      routes: [
        // Authentication routes
        GoRoute(
          path: loginPath,
          name: loginRoute,
          builder: (context, state) {
            _logger.logNavigation('route_accessed', loginPath);
            return const LoginPage();
          },
        ),
        GoRoute(
          path: signupPath,
          name: signupRoute,
          builder: (context, state) {
            _logger.logNavigation('route_accessed', signupPath);
            return const CreateAccountPage();
          },
        ),
        GoRoute(
          path: forgotPasswordPath,
          name: forgotPasswordRoute,
          builder: (context, state) {
            _logger.logNavigation('route_accessed', forgotPasswordPath);
            return const ForgotPasswordPage();
          },
        ),

        // Protected routes
        GoRoute(
          path: homePath,
          name: homeRoute,
          builder: (context, state) {
            _logger.logNavigation('route_accessed', homePath);
            return const HomePage();
          },
        ),

        // Face verification route
        GoRoute(
          path: faceVerificationPath,
          name: faceVerificationRoute,
          builder: (context, state) {
            _logger.logNavigation('route_accessed', faceVerificationPath);
            return const FaceVideoCapturePage();
          },
        ),
      ],
      errorBuilder: (context, state) {
        _logger.error(
          LoggingConstants.formatError(
            LoggingConstants.navigationModule,
            LoggingConstants.recoverableError,
            'Route error: ${state.error}',
            'Path: ${state.matchedLocation}',
          ),
          state.error,
        );

        return Scaffold(
          appBar: AppBar(
            title: const Text('Error'),
          ),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Page not found',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'The page you are looking for does not exist.',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => context.go(loginPath),
                  child: const Text('Go to Login'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Handles route redirection based on authentication state.
  static String? _redirect(BuildContext context, GoRouterState state) {
    final authCubit = getIt<AuthCubit>();
    final isAuthenticated = authCubit.isAuthenticated;
    final isUnknown = authCubit.isUnknown;
    final currentPath = state.matchedLocation;

    final details = 'Path: $currentPath, '
        'Authenticated: $isAuthenticated, Unknown: $isUnknown';

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.navigationModule,
        'Route redirect check',
        details,
      ),
    );

    // If authentication status is unknown, stay on current route
    if (isUnknown) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.navigationModule,
          'Authentication status unknown, staying on current route',
          'Path: $currentPath',
        ),
      );
      return null;
    }

    // Define protected routes
    final protectedRoutes = [homePath, faceVerificationPath];
    final authRoutes = [loginPath, signupPath, forgotPasswordPath];

    final isProtectedRoute = protectedRoutes.contains(currentPath);
    final isAuthRoute = authRoutes.contains(currentPath);

    if (isAuthenticated) {
      // User is authenticated
      if (isAuthRoute) {
        // Redirect authenticated users away from auth pages
        _logger.logNavigation(
          'redirect_authenticated_user',
          '$currentPath -> $homePath',
        );
        return homePath;
      }
      // Allow access to protected routes
      return null;
    } else {
      // User is not authenticated
      if (isProtectedRoute) {
        // Redirect unauthenticated users to login
        _logger.logNavigation(
          'redirect_unauthenticated_user',
          '$currentPath -> $loginPath',
        );
        return loginPath;
      }
      // Allow access to auth routes
      return null;
    }
  }
}

/// Extension methods for easier navigation.
extension AppRouterExtension on BuildContext {
  /// Navigate to login page.
  void goToLogin() => go(AppRouter.loginPath);

  /// Navigate to signup page.
  void goToSignup() => go(AppRouter.signupPath);

  /// Navigate to forgot password page.
  void goToForgotPassword() => go(AppRouter.forgotPasswordPath);

  /// Navigate to home page.
  void goToHome() => go(AppRouter.homePath);

  /// Navigate to face verification page.
  void goToFaceVerification() => go(AppRouter.faceVerificationPath);

  /// Navigate back.
  void goBack() => pop();
}
